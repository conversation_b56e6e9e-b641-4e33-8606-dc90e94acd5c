import {
  getSharedTestContext,
  resetSharedTestContext,
  cleanupTestState,
  executeCommand,
  expectNotice,
  expectPostFile,
  type SharedTestContext
} from '../helpers/shared-context';
import { test, beforeAll, beforeEach, afterEach } from 'vitest';

describe("Commands: Create New Post", () => {
  let context: SharedTestContext;

  beforeAll(async () => { context = await getSharedTestContext(); });

  beforeEach(async () => { await resetSharedTestContext(); });

  afterEach(async () => { await cleanupTestState(); });

  test("should create a new post using Playwright Electron", async () => {
    const testTitle = "Test Post";

    await executeCommand(context, 'Ghost Sync: Create new post');

    const inputSelector = '.ghost-sync-modal input[type="text"]';

    await context.page.waitForSelector(inputSelector);
    await context.page.fill(inputSelector, testTitle);

    const createButtonSelector = '.ghost-sync-modal button.mod-cta';
    await context.page.click(createButtonSelector);

    await expectNotice(context, "Created new post");
    await expectPostFile(context, "test-post", { content: "Write your content here." });
  });
});
